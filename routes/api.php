<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::group(['namespace' => 'App\Http\Controllers\API\V1', 'prefix' => 'v1'], function (){
    Route::post('/valid-token', 'ValidTokenController@validToken')->name('api.validToken');
    Route::get('/provinces/{id}/districts', 'AddressController@getDistrictByProvince')->name('api.provinces.getDistrictByProvince');
    Route::post('/materials', 'MaterialController@getMaterialsForAccount')->name('api.materials');
    Route::post('/materials/{id}', 'MaterialController@getMaterial')->name('api.material');
});

// Route for getting districts by province ID
Route::get('/districts', 'App\Http\Controllers\AddressController@getDistrictsByProvince')->name('api.districts');
