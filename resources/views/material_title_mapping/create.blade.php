@extends('layouts.AdminLTE.index')

@section('icon_page', 'plus')

@section('title', 'Add Material Title Mapping')

@section('menu_pagina')
<li role="presentation">
    <a href="{{ route('material_title_mapping.index') }}" class="link_menu_page">
        <i class="fa fa-list"></i> All Mappings
    </a>
</li>
@endsection

@section('content')

<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">Create New Material Title Mapping</h3>
    </div>
    
    <form action="{{ route('material_title_mapping.store') }}" method="POST">
        @csrf
        <div class="box-body">
            @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group {{ $errors->has('material_id') ? 'has-error' : '' }}">
                        <label for="material_id">Material <span class="text-red">*</span></label>
                        <select name="material_id" id="material_id" class="form-control" required>
                            <option value="">Select a material</option>
                            @foreach($materials as $material)
                                <option value="{{ $material->id }}" {{ old('material_id') == $material->id ? 'selected' : '' }}>
                                    {{ $material->title }} ({{ $material->lesson->course->title_en }})
                                </option>
                            @endforeach
                        </select>
                        @if($errors->has('material_id'))
                            <span class="help-block"><strong>{{ $errors->first('material_id') }}</strong></span>
                        @endif
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group {{ $errors->has('account_type_id') ? 'has-error' : '' }}">
                        <label for="account_type_id">Account Type <span class="text-red">*</span></label>
                        <select name="account_type_id" id="account_type_id" class="form-control" required>
                            <option value="">Select an account type</option>
                            @foreach($accountTypes as $accountType)
                                <option value="{{ $accountType->id }}" {{ old('account_type_id') == $accountType->id ? 'selected' : '' }}>
                                    {{ $accountType->name }}
                                </option>
                            @endforeach
                        </select>
                        @if($errors->has('account_type_id'))
                            <span class="help-block"><strong>{{ $errors->first('account_type_id') }}</strong></span>
                        @endif
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group {{ $errors->has('prefix_number') ? 'has-error' : '' }}">
                        <label for="prefix_number">Prefix Number <span class="text-red">*</span></label>
                        <input type="text" name="prefix_number" id="prefix_number" class="form-control" 
                               placeholder="e.g., 1, 24, 5" value="{{ old('prefix_number') }}" required>
                        <p class="help-block">The number that will appear in the title prefix (e.g., "1" will show as "Bài 1 - Material Title")</p>
                        @if($errors->has('prefix_number'))
                            <span class="help-block"><strong>{{ $errors->first('prefix_number') }}</strong></span>
                        @endif
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Title Preview</label>
                        <div class="well" id="title-preview" style="min-height: 50px; background-color: #f9f9f9;">
                            <em>Select a material and enter a prefix number to see the preview</em>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="box-footer">
            <button type="submit" class="btn btn-primary">
                <i class="fa fa-save"></i> Create Mapping
            </button>
            <a href="{{ route('material_title_mapping.index') }}" class="btn btn-default">
                <i class="fa fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const materialSelect = document.getElementById('material_id');
    const prefixInput = document.getElementById('prefix_number');
    const preview = document.getElementById('title-preview');
    
    function updatePreview() {
        const materialOption = materialSelect.options[materialSelect.selectedIndex];
        const prefixNumber = prefixInput.value.trim();
        
        if (materialSelect.value && prefixNumber) {
            const materialText = materialOption.text;
            const materialTitle = materialText.split(' (')[0]; // Extract title before course name
            preview.innerHTML = '<strong>Bài ' + prefixNumber + ' - ' + materialTitle + '</strong>';
        } else {
            preview.innerHTML = '<em>Select a material and enter a prefix number to see the preview</em>';
        }
    }
    
    materialSelect.addEventListener('change', updatePreview);
    prefixInput.addEventListener('input', updatePreview);
});
</script>

@endsection
