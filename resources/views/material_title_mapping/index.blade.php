@extends('layouts.AdminLTE.index')

@section('icon_page', 'cogs')

@section('title', 'Material Title Mappings')

@section('menu_pagina')
<li role="presentation">
    <a href="{{ route('material_title_mapping.create') }}" class="link_menu_page">
        <i class="fa fa-plus"></i> Add New Mapping
    </a>
</li>
@endsection

@section('content')

<div class="box box-primary">
    <div class="box-body">
        <div class="row">
            <form action="{{ route('material_title_mapping.index') }}" method="get">
                <div class="col-lg-3">
                    <div class="form-group">
                        <label for="account_type_id">Account Type</label>
                        <select name="account_type_id" class="form-control">
                            <option value="">All Account Types</option>
                            @foreach($accountTypes as $accountType)
                                <option value="{{ $accountType->id }}" {{ $accountTypeId == $accountType->id ? 'selected' : '' }}>
                                    {{ $accountType->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="form-group">
                        <label for="material_id">Material</label>
                        <select name="material_id" class="form-control">
                            <option value="">All Materials</option>
                            @foreach($materials as $material)
                                <option value="{{ $material->id }}" {{ $materialId == $material->id ? 'selected' : '' }}>
                                    {{ $material->title }} ({{ $material->lesson->course->title_en }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-lg-1">
                    <label for="filter">Filter</label>
                    <button type="submit" class="btn btn-google pull-right">
                        <i class="fa fa-fw fa-search"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <div class="box-body">
        @if(session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif
        
        @if(session('error'))
            <div class="alert alert-danger">
                {{ session('error') }}
            </div>
        @endif
        
        <div class="row">
            <div class="col-md-12">
                <div class="table-responsive">
                    <table class="table table-condensed table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>#</th>
                            <th>Material Title</th>
                            <th>Course</th>
                            <th>Account Type</th>
                            <th>Prefix Number</th>
                            <th>Dynamic Title Preview</th>
                            <th>Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php $cnt = ($mappings->currentPage() - 1) * $mappings->perPage() + 1; @endphp
                        @forelse($mappings as $mapping)
                        <tr>
                            <td>{{ $cnt++ }}</td>
                            <td>{{ $mapping->material->title }}</td>
                            <td>{{ $mapping->material->lesson->course->title_en }}</td>
                            <td>{{ $mapping->accountType->name }}</td>
                            <td>{{ $mapping->prefix_number }}</td>
                            <td><strong>Bài {{ $mapping->prefix_number }} - {{ $mapping->material->title }}</strong></td>
                            <td class="text-center">
                                <a class="btn btn-default btn-xs" href="{{ route('material_title_mapping.edit', $mapping->id) }}" title="Edit Mapping">
                                    <i class="fa fa-pencil"></i>
                                </a>
                                <a class="btn btn-danger btn-xs" href="#" title="Delete Mapping" data-toggle="modal" data-target="#modal-delete-{{ $mapping->id }}">
                                    <i class="fa fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                        
                        <!-- Delete Modal -->
                        <div class="modal fade" id="modal-delete-{{ $mapping->id }}">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <h4 class="modal-title"><i class="fa fa-warning"></i> Confirm Delete</h4>
                                    </div>
                                    <div class="modal-body">
                                        <p>Are you sure you want to delete this title mapping?</p>
                                        <p><strong>Material:</strong> {{ $mapping->material->title }}</p>
                                        <p><strong>Account Type:</strong> {{ $mapping->accountType->name }}</p>
                                        <p><strong>Prefix:</strong> {{ $mapping->prefix_number }}</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                                        <form action="{{ route('material_title_mapping.destroy', $mapping->id) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center">No material title mappings found.</td>
                        </tr>
                        @endforelse
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="text-center">
                    {{ $mappings->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
