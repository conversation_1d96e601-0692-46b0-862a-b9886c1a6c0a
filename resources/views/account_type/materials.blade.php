@if(isset($accountType->paginatedMaterials))
    @foreach($accountType->paginatedMaterials as $material)
        @php
            // Try to match by material content (content field in materials table)
            $materialId = null;
            $exists = false;
            $materialObject = null;

            // Check if the material exists in our database by content field
            if (array_key_exists($material, $existingMaterials)) {
                $exists = true;
                $materialId = $existingMaterials[$material];
                $materialObject = \App\Models\Material::find($materialId);
            }

            $cssClass = $exists ? 'exists' : 'not-exists';

            // Get dynamic title if material exists
            $displayTitle = $material; // Default to content identifier
            if ($materialObject) {
                $displayTitle = $materialObject->getDynamicTitle($accountType);
            }
        @endphp
        <a href="{{ $exists ? route('material.edit', $materialId) : route('material.create') . '?content=' . urlencode($material) }}"
           class="tag material-tag {{ $cssClass }}"
           data-material="{{ $material }}"
           @if($exists) data-material-id="{{ $materialId }}" @endif
           title="{{ $exists ? 'Edit material: ' . $displayTitle : 'Create new material' }}">
            @if($exists)
                <span class="dynamic-title" style="font-weight: bold; color: #333;">{{ $displayTitle }}</span>
                <br><small class="content-id" style="color: #666; font-size: 0.8em;">({{ $material }})</small>
            @else
                {{ $material }}
            @endif
        </a>
    @endforeach

    <div class="pagination-container" id="pagination-container-{{ $accountType->id }}">
        <div class="pagination-wrapper">
            @php
                $paginator = $accountType->paginatedMaterials;
                $totalPages = $paginator->lastPage();
                $currentPage = $paginator->currentPage();
            @endphp

            <ul class="pagination">
                {{-- Previous Page Link --}}
                @if ($currentPage > 1)
                    <li>
                        <a href="javascript:void(0)" class="material-page-link" data-account-type-id="{{ $accountType->id }}" data-page="{{ $currentPage - 1 }}">&laquo;</a>
                    </li>
                @else
                    <li class="disabled">
                        <span>&laquo;</span>
                    </li>
                @endif

                {{-- Page Number Links --}}
                @for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++)
                    <li class="{{ $i == $currentPage ? 'active' : '' }}">
                        <a href="javascript:void(0)" class="material-page-link" data-account-type-id="{{ $accountType->id }}" data-page="{{ $i }}">{{ $i }}</a>
                    </li>
                @endfor

                {{-- Next Page Link --}}
                @if ($currentPage < $totalPages)
                    <li>
                        <a href="javascript:void(0)" class="material-page-link" data-account-type-id="{{ $accountType->id }}" data-page="{{ $currentPage + 1 }}">&raquo;</a>
                    </li>
                @else
                    <li class="disabled">
                        <span>&raquo;</span>
                    </li>
                @endif
            </ul>

        </div>
        <div class="pagination-info">
            Showing {{ $paginator->firstItem() }} to {{ $paginator->lastItem() }} of {{ $paginator->total() }} materials
            @if($accountType->nonExistentCount > 0)
                <span class="non-existent-info">
                    ({{ $accountType->nonExistentCount }} {{ __('messages.non_existent_materials') }})
                </span>
            @endif
        </div>
    </div>
@else
    <p>No materials found.</p>
@endif
