<aside class="main-sidebar">
	<section class="sidebar">
		<ul class="sidebar-menu" data-widget="tree">
			<li class="header" style="color:#fff;"> {{ __('messages.menu_root') }} <i class="fa fa-level-down"></i></li>
			<li class="
						{{ Request::segment(1) === null ? 'active' : null }}
						{{ Request::segment(1) === 'home' ? 'active' : null }}
					  ">
				<a href="{{ route('home') }}" title="Dashboard"><i class="fa fa-dashboard"></i> <span> {{ __('messages.home') }}</span></a>
			</li>

            <li class="treeview
				{{ Request::segment(1) === 'account' ? 'active menu-open' : null }}
				{{-- Commented out pending account feature --}}
				{{-- {{ Request::segment(1) === 'account-pending' ? 'active menu-open' : null }} --}}
				{{ Request::segment(1) === 'account-type' ? 'active menu-open' : null }}
				{{ Request::segment(1) === 'account-backup' ? 'active menu-open' : null }}
				">
                <a href="#">
                    <i class="fa fa-users"></i>
                    <span>{{ __('messages.menu.account') }}</span>
                    <span class="pull-right-container">
						<i class="fa fa-angle-left pull-right"></i>
					</span>
                </a>
                <ul class="treeview-menu">
                    <li class="{{ Request::segment(1) === 'account' ? 'active' : null }}">
                        <a href="{{ route('account') }}" title="Account"><i class="fa fa-user"></i> <span> {{ __('messages.menu.account') }}</span></a>
                    </li>
                    {{-- Commented out pending account menu item --}}
                    {{-- <li class="{{ Request::segment(1) === 'account-pending' ? 'active' : null }}">
                        <a href="{{ route('account_pending') }}" title="Account Pending"><i class="fa fa-user-plus"></i> <span> {{ __('messages.menu.account_pending') }}</span></a>
                    </li> --}}
                    <li class="{{ Request::segment(1) === 'account-type' ? 'active' : null }}">
                        <a href="{{ route('account_type') }}" title="Account Type"><i class="fa fa-user-o"></i> <span> {{ __('messages.menu.account_type') }}</span></a>
                    </li>

                    @if (Auth::user()->can('root-dev', ''))
                        <li class="{{ Request::segment(1) === 'account-backup' ? 'active' : null }}">
                            <a href="{{ route('account_backup') }}" title="Account Backup"><i class="fa fa-download"></i> <span> {{ __('messages.menu.account_backup') }}</span></a>
                        </li>
                    @endif
                </ul>
            </li>

            <li class="treeview
				{{ Request::segment(1) === 'course' ? 'active menu-open' : null }}
				{{ Request::segment(1) === 'lesson' ? 'active menu-open' : null }}
				{{ Request::segment(1) === 'material-title-mapping' ? 'active menu-open' : null }}
				">
                <a href="#">
                    <i class="fa fa-codepen"></i>
                    <span>{{ __('messages.menu.course') }}</span>
                    <span class="pull-right-container">
						<i class="fa fa-angle-left pull-right"></i>
					</span>
                </a>
                <ul class="treeview-menu">
                    <li class="{{ Request::segment(1) === 'course' ? 'active' : null }}">
                        <a href="{{ route('course') }}" title="Course"><i class="fa fa-book"></i> <span> {{ __('messages.menu.course') }}</span></a>
                    </li>
                    <li class="{{ Request::segment(1) === 'lesson' ? 'active' : null }}">
                        <a href="{{ route('lesson') }}" title="Lesson"><i class="fa fa-leanpub"></i> <span> {{ __('messages.menu.lesson') }}</span></a>
                    </li>
                    <li class="{{ Request::segment(1) === 'material-title-mapping' ? 'active' : null }}">
                        <a href="{{ route('material_title_mapping.index') }}" title="Material Title Mappings"><i class="fa fa-tags"></i> <span> Material Title Mappings</span></a>
                    </li>


                </ul>
            </li>

            <li class="{{ Request::segment(1) === 'address' ? 'active' : null }}">
                <a href="{{ route('address') }}" title="Address"><i class="fa fa-address-card-o"></i> <span> {{ __('messages.menu.address') }}</span></a>
            </li>

			@if(Request::segment(1) === 'profile')

			<li class="{{ Request::segment(1) === 'profile' ? 'active' : null }}">
				<a href="{{ route('profile') }}" title="Profile"><i class="fa fa-user"></i> <span> {{ __('messages.menu.profile') }}</span></a>
			</li>

			@endif
			<li class="treeview
				{{ Request::segment(1) === 'config' ? 'active menu-open' : null }}
				{{ Request::segment(1) === 'user' ? 'active menu-open' : null }}
				{{ Request::segment(1) === 'role' ? 'active menu-open' : null }}
				">
				<a href="#">
					<i class="fa fa-gear"></i>
					<span>{{ __('messages.menu.config') }}</span>
					<span class="pull-right-container">
						<i class="fa fa-angle-left pull-right"></i>
					</span>
				</a>
				<ul class="treeview-menu">
					@if (Auth::user()->can('root-dev', ''))
						<li class="{{ Request::segment(1) === 'config' && Request::segment(2) === null ? 'active' : null }}">
							<a href="{{ route('config') }}" title="App Config">
								<i class="fa fa-gear"></i> <span> {{ __('messages.menu.config_website') }}</span>
							</a>
						</li>
					@endif
					<li class="
						{{ Request::segment(1) === 'user' ? 'active' : null }}
						{{ Request::segment(1) === 'role' ? 'active' : null }}
						">
						<a href="{{ route('user') }}" title="Users">
							<i class="fa fa-user"></i> <span> {{ __('messages.menu.user') }}</span>
						</a>
					</li>
				</ul>
			</li>
		</ul>
	</section>
</aside>
