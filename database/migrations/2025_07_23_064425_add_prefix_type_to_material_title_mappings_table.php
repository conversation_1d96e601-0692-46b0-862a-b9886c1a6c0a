<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPrefixTypeToMaterialTitleMappingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('material_title_mappings', function (Blueprint $table) {
            $table->string('prefix_type')->default('Bài')->after('prefix_number');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('material_title_mappings', function (Blueprint $table) {
            $table->dropColumn('prefix_type');
        });
    }
}
