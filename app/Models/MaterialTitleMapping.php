<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaterialTitleMapping extends Model
{
    use HasFactory;

    protected $fillable = [
        'material_id',
        'account_type_id',
        'prefix_number',
        'prefix_type'
    ];

    /**
     * Get available prefix types
     */
    public static function getPrefixTypes()
    {
        return [
            'Bài' => 'Bài',
            'Chuyên đề' => 'Chuyên đề',
            'Hoạt động' => 'Hoạt động',
            'Hè' => 'Hè',
            'Chủ đề' => 'Chủ đề',
            'Phần' => 'Phần',
            'Tiết' => 'Tiết',
        ];
    }

    /**
     * Get the material that owns the title mapping.
     */
    public function material()
    {
        return $this->belongsTo(Material::class);
    }

    /**
     * Get the account type that owns the title mapping.
     */
    public function accountType()
    {
        return $this->belongsTo(AccountType::class);
    }
}
