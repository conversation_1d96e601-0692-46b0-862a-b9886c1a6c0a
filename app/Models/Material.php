<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Material extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['lesson_id', 'title', 'type', 'content', 'content_url', 'deleted_at', 'document_url'];

    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }

    /**
     * Get the title mappings for this material.
     */
    public function titleMappings()
    {
        return $this->hasMany(MaterialTitleMapping::class);
    }

    /**
     * Get the dynamic title for this material based on account type.
     *
     * @param AccountType|int|null $accountType The account type or account type ID
     * @return string The formatted title with prefix or original title if no mapping exists
     */
    public function getDynamicTitle($accountType = null)
    {
        // If no account type provided, return original title
        if (!$accountType) {
            return $this->title;
        }

        // Get account type ID
        $accountTypeId = is_object($accountType) ? $accountType->id : $accountType;

        // Find the title mapping for this material and account type
        $mapping = $this->titleMappings()
            ->where('account_type_id', $accountTypeId)
            ->first();

        // If mapping exists, return formatted title with prefix
        if ($mapping) {
            // Get the base title without any existing "Bài X - " prefix
            $baseTitle = $this->getBaseTitleWithoutPrefix();
            return "Bài " . $mapping->prefix_number . " - " . $baseTitle;
        }

        // Return original title if no mapping exists
        return $this->title;
    }

    /**
     * Get the base title without any "Bài X - " prefix.
     * This handles cases where materials were imported with the old prefix system.
     *
     * @return string The title without prefix
     */
    public function getBaseTitleWithoutPrefix()
    {
        // Remove "Bài X - " pattern from the beginning of the title
        $title = $this->title;

        // Pattern to match "Bài [number] - " at the beginning
        $pattern = '/^Bài\s+\d+\s*-\s*/';

        if (preg_match($pattern, $title)) {
            $title = preg_replace($pattern, '', $title);
        }

        return $title;
    }

    /**
     * Get the dynamic title for this material based on account type (for API responses).
     * This method is similar to getDynamicTitle but can be used in API serialization.
     *
     * @param AccountType|int|null $accountType The account type or account type ID
     * @return string The formatted title with prefix or original title if no mapping exists
     */
    public function getFormattedTitleAttribute($accountType = null)
    {
        return $this->getDynamicTitle($accountType);
    }
}
