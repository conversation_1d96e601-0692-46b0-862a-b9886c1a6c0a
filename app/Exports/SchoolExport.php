<?php

namespace App\Exports;

use App\Http\Helpers;
use App\Models\School;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class SchoolExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;
    private $stt;

    public function __construct()
    {
        $this->stt = 1;
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        // Only export 'customer' schools (exclude partners and internal)
        return School::query()
            ->whereRaw("LOWER(name) NOT LIKE '%novastars%'")
            ->where(function($query) {
                $query->where('name', 'like', '%TH%')
                    ->orWhere('name', 'like', '%MN%')
                    ->orWhere('name', 'like', '%tiểu học%')
                    ->orWhere('name', 'like', '%tieu hoc%')
                    ->orWhere('name', 'like', '%mầm non%')
                    ->orWhere('name', 'like', '%mam non%');
            })
            ->orderBy('province')
            ->orderBy('district')
            ->orderBy('name')
            ->get();
    }

    public function headings(): array
    {
        return [
            __('messages.stt'),
            __('messages.name_org'),
            __('messages.province'),
            __('messages.district'),
            __('messages.time_expired'),
            __('messages.software_type'),
            __('messages.account_type'),
            __('messages.school_type'),
        ];
    }

    public function map($row): array
    {
        $provinces = Helpers::getProvinces();
        $districts = Helpers::getDistricts();
        // Software type in Vietnamese
        $softwareTypes = [];
        if ($row->is_mamnon) {
            $softwareTypes[] = __('messages.software_mamnon');
        }
        if ($row->is_tieuhoc) {
            $softwareTypes[] = __('messages.software_tieuhoc');
        }
        // School type in Vietnamese
        $schoolType = '';
        if ($row->school_type === 'customer') {
            $schoolType = __('messages.customer');
        } elseif ($row->school_type === 'partner') {
            $schoolType = __('messages.partner');
        } elseif ($row->school_type === 'internal') {
            $schoolType = __('messages.internal');
        } else {
            $schoolType = $row->school_type;
        }
        return [
            $this->stt++,
            $row->name,
            $row->province != null && array_key_exists($row->province, $provinces) ? $provinces[$row->province]['name'] : null,
            $row->district != null && array_key_exists($row->district, $districts) ? $districts[$row->district]['name'] : null,
            $row->time_expired ? \Carbon\Carbon::parse($row->time_expired)->format('d-m-Y') : null,
            implode(', ', $softwareTypes),
            ($row->mamnonAccountType ? $row->mamnonAccountType->code : '') . ($row->tieuhocAccountType ? ( ($row->mamnonAccountType ? ', ' : '') . $row->tieuhocAccountType->code) : ''),
            $schoolType,
        ];
    }
} 