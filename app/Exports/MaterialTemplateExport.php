<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

class MaterialTemplateExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;

    /**
    * @return Collection
    */
    public function collection()
    {
        // Return sample data for template matching the exact format from screenshot
        return collect([
            [
                'title' => 'Ôn tương đầu tiên của em',
                'url' => '/he-1-01',
                'content' => 'h1.01',
                'document' => 'a',
                'course' => 'LTH'
            ],
            [
                'title' => 'Em tìm hiểu nơi quy học tiểu học',
                'url' => '/he-1-02',
                'content' => 'h1.02',
                'document' => 'a',
                'course' => 'LTH'
            ]
        ]);
    }

    public function headings(): array
    {
        return [
            'Tiêu đề',
            'URL',
            'Nội dung',
            'Giáo án',
            'Khóa học'
        ];
    }

    public function map($row): array
    {
        return [
            $row['title'],
            $row['url'],
            $row['content'],
            $row['document'],
            $row['course']
        ];
    }
}
