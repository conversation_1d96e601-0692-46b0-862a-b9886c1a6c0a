<?php

namespace App\Exports;

use App\Http\Helpers;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class AddressExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    public function collection(): \Illuminate\Support\Collection
    {
        $provinces = Helpers::getProvinces();
        $districts = Helpers::getDistricts();

        $result = array();
        foreach ($districts as $district){
            $districtName = $district['name'];
            $districtCode = $district['code'];
            $provinceCode = $district['parent_code'];
            $province = $provinces[$provinceCode];

            $result[] = [
                'name' => $districtName,
                'code' => $districtCode,
                'province_name' => $province['name']
            ];
        }

        return collect($result);
    }

    public function headings(): array
    {
        return [
            'Tên huyện',
            'Mã huyện',
            'Tên tỉnh'
        ];
    }
}
