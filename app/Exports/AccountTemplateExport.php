<?php

namespace App\Exports;

use App\Http\Constants;
use App\Http\Helpers;
use App\Models\Account;
use App\Models\AccountType;
use Exception;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AccountTemplateExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;

    /**
    * @return Collection
    */
    public function collection()
    {
        return Account::query()
            ->limit(5)
            ->get();
    }

    public function headings(): array
    {
        return [
            'Tên',
            'Trường/ trung tâm',
            'SĐT',
            'E-mail',
            'Tỉnh/ Thành phố',
            'Huyện/ Thị xã',
            'Lớp',
            'Trạng thái',
            '<PERSON>ần mềm mầm non',
            '<PERSON>ần mềm tiểu học',
            '<PERSON><PERSON><PERSON> phần mềm mầm non',
            '<PERSON><PERSON><PERSON> phần mềm tiểu học',
            'Ngày hết hạn'
        ];
    }

    /**
     * @throws Exception
     */
    public function map($row): array
    {
        $provinces = Helpers::getProvinces();
        $districts = Helpers::getDistricts();
        $mamnonAccountType = null;
        $tieuhocAccountType = null;

        if($row->is_mamnon == 1){
            $mamnonAccountType = AccountType::query()->where('id', $row->mamnon_account_type_id)->first();
        }

        if($row->is_tieuhoc == 1){
            $tieuhocAccountType = AccountType::query()->where('id', $row->tieuhoc_account_type_id)->first();
        }

        return [
            $row->name,
            $row->name_org,
            $row->phone_number,
            $row->email,
            $row->province != null && array_key_exists($row->province, $provinces)? $provinces[$row->province]['name'] : null,
            $row->district != null && array_key_exists($row->district, $districts)? $districts[$row->district]['name'] : null,
            $row->class,
            $row->status,
            $row->is_mamnon == 1? "x" : "",
            $row->is_tieuhoc == 1? "x" : "",
            $mamnonAccountType != null? $mamnonAccountType->code : null,
            $tieuhocAccountType != null? $tieuhocAccountType->code : null,
            now()->addMonth()->format(Constants::FORMAT_DATE_USER)
        ];
    }
}
