<?php

namespace App\Exports;

use App\Models\Material;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Support\Collection;

class MaterialExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;
    private $stt;

    public function __construct()
    {
        $this->stt = 0;
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return Material::query()
            ->select([
                'id',
                'title',
                'content_url',
                'content',
                'document_url',
                'lesson_id',
                'created_at'
            ])
            ->with(['lesson.course'])
            ->orderBy('created_at', 'asc') // Maintain import order in export
            ->get();
    }

    public function headings(): array
    {
        return [
            'STT',
            'Tiêu đề',
            'URL',
            'Nội dung',
            'Gi<PERSON>o án',
            '<PERSON>h<PERSON>a học',
            'Tên khóa học',
            'Tên bài học'
        ];
    }

    public function map($material): array
    {
        $courseKey = '';
        $courseName = '';
        $lessonName = '';

        if ($material->lesson && $material->lesson->course) {
            $courseKey = $material->lesson->course->key ?: $material->lesson->course->id;
            $courseName = $material->lesson->course->title_en ?? '';
            $lessonName = $material->lesson->title ?? '';
        }

        return [
            ++$this->stt,
            $material->title,
            $material->content_url,
            $material->content,
            $material->document_url,
            $courseKey,
            $courseName,
            $lessonName
        ];
    }
}
