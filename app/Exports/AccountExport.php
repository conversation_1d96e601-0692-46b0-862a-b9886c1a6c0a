<?php

namespace App\Exports;

use App\Http\Helpers;
use App\Models\Account;
use DateTime;
use Exception;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AccountExport implements FromCollection, WithHeadings, ShouldAutoSize, WithMapping
{
    use Exportable;
    private $stt;

    public function __construct()
    {
        $this->stt = 0;
    }

    /**
    * @return Collection
    */
    public function collection(): Collection
    {
        return Account::query()->select([
            'name',
            'name_org',
            'phone_number',
            'email',
            'province',
            'district',
            'class',
            'status',
            'time_expired',
            'created_at'
        ])
            ->get();
    }

    public function headings(): array
    {
        return [
            __('messages.count'),
            __('messages.name'),
            __('messages.name_org'),
            __('messages.phone_number'),
            __('messages.email'),
            __('messages.province'),
            __('messages.district'),
            __('messages.class'),
            __('messages.status'),
            __('messages.time_expired'),
            __('messages.created_at')
        ];
    }

    /**
     * @throws Exception
     */
    public function map($row): array
    {
        $provinces = Helpers::getProvinces();
        $districts = Helpers::getDistricts();

        return [
            $this->stt++,
            $row->name,
            $row->name_org,
            $row->phone_number,
            $row->email,
            $row->province != null && array_key_exists($row->province, $provinces)? $provinces[$row->province]['name'] : null,
            $row->district != null && array_key_exists($row->district, $districts)? $districts[$row->district]['name'] : null,
            $row->class,
            $row->status == 1? __('messages.status.active') : __('messages.status.locked'),
            $row->time_expired != null ? \Carbon\Carbon::parse($row->time_expired)->format('d-m-Y') : null,
            $row->created_at->format('d/m/Y H:i')
        ];
    }
}
