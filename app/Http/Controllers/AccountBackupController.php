<?php

namespace App\Http\Controllers;

use App\Models\AccountBackup;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;

class AccountBackupController extends Controller
{
    public function index()
    {
        $this->authorize('root-dev', AccountBackup::class);
        $files = AccountBackup::query()
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('account_backup.index',compact('files'));
    }

    public function download($id){
        $this->authorize('root-dev', AccountBackup::class);
        $file = AccountBackup::query()->where('id',$id)->first();

        if($file == null){
            $this->flashMessage('warning', __('messages.alert.add.not_found'), 'danger');
            return redirect()->route('account_backup');
        }

        $fileName = $file->file_name;
        $path = storage_path('app/public') . '/' . AccountBackup::BACKUP_FOLDER . '/' . $fileName;
        $response = new BinaryFileResponse($path);
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            basename($path)
        );

        return $response;
    }
}
