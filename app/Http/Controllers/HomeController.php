<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Http\Response;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $totalAccount = Account::query()->where('deleted_at', null)->count();
        $totalAccountActive = Account::query()->where('status', 1)->where('deleted_at', null)->count();
        $totalAccountLocked = Account::query()->where('status', 0)->where('deleted_at', null)->count();
        $totalUser = User::query()->count();

        return view('home', compact('totalAccount', 'totalAccountActive', 'totalAccountLocked', 'totalUser'));
    }
}
