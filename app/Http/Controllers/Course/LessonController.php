<?php

namespace App\Http\Controllers\Course;

use App\Http\Controllers\Controller;
use App\Http\Requests\Lesson\StoreRequest;
use App\Http\Requests\Lesson\UpdateRequest;
use App\Models\Course;
use App\Models\Lesson;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class LessonController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize('show-course', Lesson::class);
        $keyword = $request->input('search');

        $query = Lesson::query()
            ->with('course');

        if($keyword != null){
            $query = $query->where('title', 'like', "%$keyword%")
                ->orWhere('description', 'like', "%$keyword%");
        }

        $lessons = $query
            ->orderBy('created_at', 'desc')
            ->paginate(100);

        return view('lesson.index', compact('lessons', 'keyword'));
    }

    public function create(Request $request)
    {
        $this->authorize('create-course', Lesson::class);
        $courses = Course::all();
        $title = $request->input('title', '');
        return view('lesson.create', compact('courses', 'title'));
    }

    public function store(StoreRequest $request)
    {
        $this->authorize('create-course', Lesson::class);

        Lesson::query()->create([
            'title' => $request->input('title'),
            'course_id' => $request->input('course'),
        ]);

        $this->flashMessage('check', 'Lesson successfully added!', 'success');
        return redirect()->route('lesson');
    }

    public function edit($id)
    {
        $this->authorize('edit-course', Lesson::class);
        $lesson = Lesson::query()
            ->find($id);
        $courses = Course::all();

        return view('lesson.edit',compact('lesson', 'courses'));
    }

    public function update(UpdateRequest $request, $id)
    {
        $this->authorize('edit-course', Lesson::class);
        $lesson = Lesson::query()->where('id', $id)->first();

        if(!$lesson){
            $this->flashMessage('warning', 'Lesson not found!', 'danger');
            return redirect()->route('lesson');
        }


        $lesson->fill([
            'title' => $request->input('title'),
            'course_id' => $request->input('course'),
        ]);
        $lesson->save();

        $this->flashMessage('check', 'Lesson successfully edited!', 'success');
        return redirect()->route('lesson');
    }

    public function destroy($id): RedirectResponse
    {
        $this->authorize('destroy-course', Lesson::class);
        $lesson = Lesson::query()->find($id);

        if(!$lesson){
            $this->flashMessage('warning', 'Lesson not found!', 'danger');
            return redirect()->route('lesson');
        }

        $lesson->delete();
        $this->flashMessage('check', 'Lesson successfully deleted!', 'success');

        return redirect()->route('lesson');
    }


}
