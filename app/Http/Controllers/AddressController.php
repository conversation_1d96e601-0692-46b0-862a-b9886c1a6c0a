<?php

namespace App\Http\Controllers;

use App\Exports\AddressExport;
use App\Http\Helpers;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class AddressController extends Controller
{
    public function index()
    {
        return view('address.index');
    }

    public function download()
    {
        return Excel::download(new AddressExport(), 'address.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    /**
     * Get districts by province ID
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDistrictsByProvince(Request $request)
    {
        $provinceId = $request->input('province_id');

        if (!$provinceId) {
            return response()->json([]);
        }

        try {
            $districts = Helpers::getDistrictsByProvinceId($provinceId);
            return response()->json($districts);
        } catch (\Exception $e) {
            return response()->json([]);
        }
    }
}
