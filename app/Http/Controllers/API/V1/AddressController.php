<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Helpers;

class AddressController extends Controller
{
    public function __construct()
    {
    }

    public function getDistrictByProvince($id): array
    {
        try {
            $districts = Helpers::getDistrictsByProvinceId($id);
        } catch (\Exception $e) {
            return [
                'error' => true,
            ];
        }

        return [
            'error' => false,
            'data' => $districts
        ];
    }
}
