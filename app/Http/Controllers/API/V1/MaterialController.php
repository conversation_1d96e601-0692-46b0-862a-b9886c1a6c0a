<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Material;
use App\Models\Account;
use App\Models\AccountType;

class MaterialController extends Controller
{
    /**
     * Get materials for a specific account with dynamic titles based on account type.
     *
     * @param Request $request
     * @return array
     */
    public function getMaterialsForAccount(Request $request): array
    {
        $token = $request->input('token');
        $softwareType = $request->input('software_type');
        $courseId = $request->input('course_id'); // Optional filter by course

        if (!$token || !$softwareType) {
            return [
                'error' => true,
                'message' => 'Token and software_type are required'
            ];
        }

        // Find the account
        $account = Account::query()
            ->where(function($q) use ($token){
                $q->where('token', $token)
                    ->orWhere('email', $token)
                    ->orWhere('phone_number', $token);
            })
            ->where(function ($q) use ($softwareType){
                $q->where(function ($q) use ($softwareType){
                    $q->whereRaw("$softwareType = " . Account::SOFTWARE_MAMNON)
                        ->where('is_mamnon', 1);
                })
                ->orWhere(function ($q) use ($softwareType){
                    $q->whereRaw("$softwareType = " . Account::SOFTWARE_TIEUHOC)
                        ->where('is_tieuhoc', 1);
                });
            })
            ->where('status', Account::STATUS_ACTIVE)
            ->whereNull('deleted_at')
            ->first();

        if (!$account) {
            return [
                'error' => true,
                'message' => 'Account not found or inactive'
            ];
        }

        // Get account type
        $accountType = null;
        if($softwareType == Account::SOFTWARE_MAMNON){
            $accountType = AccountType::query()->where('id', $account->mamnon_account_type_id)->first();
        }

        if($softwareType == Account::SOFTWARE_TIEUHOC){
            $accountType = AccountType::query()->where('id', $account->tieuhoc_account_type_id)->first();
        }

        if (!$accountType) {
            return [
                'error' => true,
                'message' => 'Account type not found'
            ];
        }

        // Build materials query
        $query = Material::query()
            ->with(['lesson.course', 'titleMappings' => function($q) use ($accountType) {
                $q->where('account_type_id', $accountType->id);
            }]);

        // Filter by course if provided
        if ($courseId) {
            $query->whereHas('lesson.course', function($q) use ($courseId) {
                $q->where('id', $courseId);
            });
        }

        $materials = $query->get();

        // Transform materials with dynamic titles
        $materialsData = $materials->map(function($material) use ($accountType) {
            return [
                'id' => $material->id,
                'title' => $material->getDynamicTitle($accountType),
                'original_title' => $material->title,
                'content' => $material->content,
                'content_url' => $material->content_url,
                'document_url' => $material->document_url,
                'lesson' => [
                    'id' => $material->lesson->id,
                    'title' => $material->lesson->title,
                ],
                'course' => [
                    'id' => $material->lesson->course->id,
                    'title' => $material->lesson->course->title_en,
                    'key' => $material->lesson->course->key,
                ],
                'created_at' => $material->created_at->toISOString(),
            ];
        });

        return [
            'error' => false,
            'account_type' => [
                'id' => $accountType->id,
                'name' => $accountType->name,
            ],
            'materials' => $materialsData,
            'total_count' => $materialsData->count()
        ];
    }

    /**
     * Get a specific material with dynamic title based on account type.
     *
     * @param Request $request
     * @param int $materialId
     * @return array
     */
    public function getMaterial(Request $request, $materialId): array
    {
        $token = $request->input('token');
        $softwareType = $request->input('software_type');

        if (!$token || !$softwareType) {
            return [
                'error' => true,
                'message' => 'Token and software_type are required'
            ];
        }

        // Find the account (similar logic as above)
        $account = Account::query()
            ->where(function($q) use ($token){
                $q->where('token', $token)
                    ->orWhere('email', $token)
                    ->orWhere('phone_number', $token);
            })
            ->where(function ($q) use ($softwareType){
                $q->where(function ($q) use ($softwareType){
                    $q->whereRaw("$softwareType = " . Account::SOFTWARE_MAMNON)
                        ->where('is_mamnon', 1);
                })
                ->orWhere(function ($q) use ($softwareType){
                    $q->whereRaw("$softwareType = " . Account::SOFTWARE_TIEUHOC)
                        ->where('is_tieuhoc', 1);
                });
            })
            ->where('status', Account::STATUS_ACTIVE)
            ->whereNull('deleted_at')
            ->first();

        if (!$account) {
            return [
                'error' => true,
                'message' => 'Account not found or inactive'
            ];
        }

        // Get account type
        $accountType = null;
        if($softwareType == Account::SOFTWARE_MAMNON){
            $accountType = AccountType::query()->where('id', $account->mamnon_account_type_id)->first();
        }

        if($softwareType == Account::SOFTWARE_TIEUHOC){
            $accountType = AccountType::query()->where('id', $account->tieuhoc_account_type_id)->first();
        }

        if (!$accountType) {
            return [
                'error' => true,
                'message' => 'Account type not found'
            ];
        }

        // Find the material
        $material = Material::with(['lesson.course', 'titleMappings' => function($q) use ($accountType) {
            $q->where('account_type_id', $accountType->id);
        }])->find($materialId);

        if (!$material) {
            return [
                'error' => true,
                'message' => 'Material not found'
            ];
        }

        return [
            'error' => false,
            'material' => [
                'id' => $material->id,
                'title' => $material->getDynamicTitle($accountType),
                'original_title' => $material->title,
                'content' => $material->content,
                'content_url' => $material->content_url,
                'document_url' => $material->document_url,
                'lesson' => [
                    'id' => $material->lesson->id,
                    'title' => $material->lesson->title,
                ],
                'course' => [
                    'id' => $material->lesson->course->id,
                    'title' => $material->lesson->course->title_en,
                    'key' => $material->lesson->course->key,
                ],
                'created_at' => $material->created_at->toISOString(),
            ],
            'account_type' => [
                'id' => $accountType->id,
                'name' => $accountType->name,
            ]
        ];
    }
}
