<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\ValidToken\ValidTokenRequest;
use App\Models\Account;
use App\Models\AccountType;
use App\Models\Config;
use App\Models\LoginHistory;

class ValidTokenController extends Controller
{
    public function validToken(ValidTokenRequest $request): array
    {
        $token = $request->input('token');
        $softwareType = $request->input('software_type');

        // check account exist
        $account = Account::query()
            ->where(function($q) use ($token){
                $q->where('token', $token)
                    ->orWhere('email', $token)
                    ->orWhere('phone_number', $token);
            })
            ->where(function ($q) use ($softwareType){
                $q->where(function ($q) use ($softwareType){
                    $q->whereRaw("$softwareType = " . Account::SOFTWARE_MAMNON)
                        ->where('is_mamnon', 1);
                })
                ->orWhere(function ($q) use ($softwareType){
                    $q->whereRaw("$softwareType = " . Account::SOFTWARE_TIEUHOC)
                        ->where('is_tieuhoc', 1);
                });
            })
            ->where('status', Account::STATUS_ACTIVE)
            ->whereNull('deleted_at')
            ->first();

        // token not correct
        if($account == null){
            return [
                'error' => true,
                'message' => "Tài khoản không tồn tại, vui lòng kiểm tra lại số điện thoại/email",
            ];
        }

        // check expired token
        $accountActive = Account::query()
            ->where(function($q) use ($token){
                $q->where('token', $token)
                    ->orWhere('email', $token)
                    ->orWhere('phone_number', $token);
            })
            ->where('status', Account::STATUS_ACTIVE)
            ->whereNotNull('time_expired')
            ->whereDate('time_expired', '>', now())
            ->whereNull('deleted_at')
            ->first();

        // token expired, write log and return
        if($accountActive == null){
            $history = new LoginHistory([
                'account_id' => $account->id,
                'ip' => $request->ip(),
                'ram_computer' => $request->input('ram_computer'),
                'cpu_computer' => $request->input('cpu_computer'),
                'vendor_computer' => $request->input('vendor_computer'),
                'status' => LoginHistory::STATUS_FAIL
            ]);
            $history->save();

            return [
                'error' => true,
                'message' => "Tài khoản hết hạn",
            ];
        }

        // token no expired
        // check ram, cpu, vendor
        $config = Config::query()->first();
        if($config->is_check_ram_cpu_vendor_computer == 1){
            $loginHistoryLatest = LoginHistory::query()
                ->where('account_id', $account->id)
                ->where('status', LoginHistory::STATUS_SUCCESS)
                ->orderBy('created_at', 'desc')
                ->first();

            if($loginHistoryLatest != null){
                $isTrueRamComputer = true;
                $isTrueCpuComputer = true;
                $isTrueVendorComputer = true;

                if($accountActive->ram_computer != null){
                    if($accountActive->ram_computer != $request->input('ram_computer')){
                        $isTrueRamComputer = false;
                    }
                }

                if($accountActive->cpu_computer != null){
                    if($accountActive->cpu_computer != $request->input('cpu_computer')){
                        $isTrueCpuComputer = false;
                    }
                }

                if($accountActive->vendor_computer != null){
                    if($accountActive->vendor_computer != $request->input('vendor_computer')){
                        $isTrueVendorComputer = false;
                    }
                }

                if(!$isTrueCpuComputer || !$isTrueRamComputer || !$isTrueVendorComputer){
                    return [
                        'error' => true,
                        'message' => "Tài khoản đã được sử dụng để đăng nhập trên máy tính khác",
                    ];
                }
            }

            $accountActive->fill([
                'ram_computer' => $request->input('ram_computer'),
                'cpu_computer' => $request->input('cpu_computer'),
                'vendor_computer' => $request->input('vendor_computer')
            ]);
            $accountActive->save();

            // verify success, save log success and pass login
            $history = new LoginHistory([
                'account_id' => $accountActive->id,
                'ip' => $request->input('ip'),
                'ram_computer' => $request->input('ram_computer'),
                'cpu_computer' => $request->input('cpu_computer'),
                'vendor_computer' => $request->input('vendor_computer'),
                'status' => LoginHistory::STATUS_SUCCESS
            ]);
            $history->save();
        }

        // get account type
        $accountType = null;
        if($softwareType == Account::SOFTWARE_MAMNON){
            $accountType = AccountType::query()->where('id', $accountActive->mamnon_account_type_id)->first();
        }

        if($softwareType == Account::SOFTWARE_TIEUHOC){
            $accountType = AccountType::query()->where('id', $accountActive->tieuhoc_account_type_id)->first();
        }

        return [
            'error' => false,
            'time_expired' => $accountActive->time_expired,
            'account_type' => $accountType
        ];
    }
}
