<?php

namespace App\Http\Controllers;

use App\Http\Requests\AccountType\StoreRequest;
use App\Http\Requests\AccountType\UpdateRequest;
use App\Models\AccountType;
use App\Models\AccountTypeCourseMaterial;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Material;

class AccountTypeController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize('show-account', AccountType::class);

        // Get search, filter and sort parameters
        $search = $request->input('search');
        $filter = $request->input('filter');
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'asc');
        $perPage = $request->input('per_page', 15);

        // Start building the query
        $query = AccountType::query()->with('courses');

        // Apply search if provided
        if (!empty($search)) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply filter if provided
        if (!empty($filter)) {
            switch ($filter) {
                case 'has_materials':
                    $query->where(function($q) {
                        $q->whereNotNull('lesson_list')
                          ->where('lesson_list', '<>', '')
                          ->where('lesson_list', '<>', '[]');
                    });
                    break;
                case 'no_materials':
                    $query->where(function($q) {
                        $q->whereNull('lesson_list')
                          ->orWhere('lesson_list', '')
                          ->orWhere('lesson_list', '[]');
                    });
                    break;
                case 'has_courses':
                    $query->whereHas('courses');
                    break;
                case 'no_courses':
                    $query->doesntHave('courses');
                    break;
            }
        }

        // Apply sorting
        if (in_array($sortField, ['name', 'code', 'description', 'created_at'])) {
            $query->orderBy($sortField, $sortDirection);
        }

        // Paginate the results
        $accountTypes = $query->paginate($perPage)->appends($request->except('page'));

        // Process account types to paginate material lists and count non-existent materials
        foreach ($accountTypes as $accountType) {
            $this->processMaterialList($accountType);
        }

        // Build existingMaterials as content => id
        $existingMaterials = Material::all()->pluck('id', 'content')->toArray();

        return view('account_type.index', compact('accountTypes', 'existingMaterials'));
    }

    public function create()
    {
        $this->authorize('create-account', AccountType::class);
        $courses = Course::all();
        return view('account_type.create', compact('courses'));
    }

    public function store(StoreRequest $request)
    {
        $this->authorize('create-account', AccountType::class);

        DB::beginTransaction();

        // save account type
        $frequency = $request->input('materials_update_frequency', 'weekly');
        $customDays = null;
        if ($frequency === 'daily') {
            $customDays = 1;
        } elseif ($frequency === 'weekly') {
            $customDays = 7;
        } elseif ($frequency === 'monthly') {
            $from = $accountType->last_materials_update ? new \DateTime($accountType->last_materials_update) : new \DateTime();
            $to = (clone $from)->add(new \DateInterval('P1M'));
            $customDays = $from->diff($to)->days;
        } elseif ($frequency === 'custom') {
            $customDays = $request->input('custom_frequency_value') !== null ? (int)$request->input('custom_frequency_value') : null;
        }
        $accountType = new AccountType();
        $accountType->fill([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'lesson_list' => $request->input('lesson_list'),
            'code' => $request->input('code'),
            'auto_manage_materials' => $request->has('auto_manage_materials') ? 1 : 0,
            'max_materials' => $request->input('max_materials', 1),
            'materials_add_count' => $request->input('materials_add_count', 1),
            'materials_update_frequency' => $frequency,
            'custom_frequency_days' => $customDays
        ]);
        $accountType->save();

        // save account type course
        $courseIds = $request->input('courses');
        $accountType->courses()->sync($courseIds);

        DB::commit();

        $this->flashMessage('check', __('messages.alert.add.successfully'), 'success');
        return redirect()->route('account_type.edit', ['id' => $accountType->id]);
    }

    public function edit($id)
    {
        $this->authorize('edit-account', AccountType::class);

        $accountType = AccountType::query()
            ->with(['courses', 'courseMaterials'])
            ->find($id);
        if(!$accountType){
            $this->flashMessage('warning', __('messages.alert.add.not_found'), 'danger');
            return redirect()->route('account_type');
        }

        $accountTypeCourseIds = array();
        if($accountType->courses != null){
            $accountTypeCourseIds = $accountType->courses->pluck('id')->toArray();
        }

        // Order courses by ID to ensure consistent ordering
        $courses = Course::orderBy('id')->get();
        return view('account_type.edit',compact('accountType', 'accountTypeCourseIds', 'courses'));
    }

    public function update(UpdateRequest $request, $id)
    {
        $this->authorize('edit-account', AccountType::class);

        $accountType = AccountType::query()->find($id);

        if(!$accountType){
            $this->flashMessage('warning', __('messages.alert.add.not_found'), 'danger');
            return redirect()->route('role');
        }

        DB::beginTransaction();

        $frequency = $request->input('materials_update_frequency', 'weekly');
        $customDays = null;
        if ($frequency === 'daily') {
            $customDays = 1;
        } elseif ($frequency === 'weekly') {
            $customDays = 7;
        } elseif ($frequency === 'monthly') {
            $from = $accountType->last_materials_update ? new \DateTime($accountType->last_materials_update) : new \DateTime();
            $to = (clone $from)->add(new \DateInterval('P1M'));
            $customDays = $from->diff($to)->days;
        } elseif ($frequency === 'custom') {
            $customDays = $request->input('custom_frequency_value') !== null ? (int)$request->input('custom_frequency_value') : null;
        }
        $accountType->materials_update_frequency = $frequency;
        $accountType->custom_frequency_days = $customDays;
        $accountType->name = $request->input('name');
        $accountType->description = $request->input('description');
        $accountType->code = $request->input('code');
        $accountType->auto_manage_materials = $request->has('auto_manage_materials') ? 1 : 0;
        $accountType->max_materials = $request->input('max_materials', 1);
        $accountType->materials_add_count = $request->input('materials_add_count', 1);
        $accountType->save();

        // save account type course
        $courseIds = $request->input('courses', []);
        $accountType->courses()->sync($courseIds);

        // Save per-course materials (only lesson_list now)
        $courseMaterialsInput = $request->input('course_materials', []);
        foreach ($courseIds as $courseId) {
            $data = $courseMaterialsInput[$courseId] ?? [];
            $cm = AccountTypeCourseMaterial::firstOrNew([
                'account_type_id' => $accountType->id,
                'course_id' => $courseId,
            ]);
            $cm->lesson_list = $data['lesson_list'] ?? '';
            $cm->save();
        }
        // Remove course materials for deselected courses
        AccountTypeCourseMaterial::where('account_type_id', $accountType->id)
            ->whereNotIn('course_id', $courseIds)
            ->delete();

        // Aggregate all per-course materials and update lesson_list for legacy compatibility
        $allMaterials = AccountTypeCourseMaterial::where('account_type_id', $accountType->id)
            ->pluck('lesson_list')
            ->filter()
            ->flatMap(function ($list) {
                return explode(',', $list);
            })
            ->unique()
            ->filter()
            ->values()
            ->all();
        $accountType->lesson_list = implode(',', $allMaterials);
        $now = now(); // Fix: define $now before using it
        $accountType->last_materials_update = $now;
        $accountType->save();

        // After updating per-course lesson_list, update the global lesson_list to be the union of all per-course lists
        $allMaterials = AccountTypeCourseMaterial::where('account_type_id', $accountType->id)
            ->pluck('lesson_list')
            ->filter()
            ->flatMap(function ($list) {
                return explode(',', $list);
            })
            ->unique()
            ->filter()
            ->values()
            ->all();
        $accountType->lesson_list = implode(',', $allMaterials);
        $accountType->last_materials_update = $now;
        $accountType->save();

        DB::commit();

        $this->flashMessage('check', __('messages.alert.edit.successfully'), 'success');
        return redirect()->route('account_type.edit', ['id' => $id]);
    }

    public function destroy($id)
    {
        $this->authorize('destroy-account', AccountType::class);

        $at = AccountType::query()->find($id);
        if(!$at){
            $this->flashMessage('warning', __('messages.alert.add.not_found'), 'danger');
            return redirect()->route('account_type');
        }

        $at->delete();
        $this->flashMessage('check', __('messages.alert.delete.successfully'), 'success');

        return redirect()->route('account_type');
    }

    /**
     * Get materials for an account type with pagination via AJAX
     */
    public function getMaterials(Request $request)
    {
        $accountTypeId = $request->input('account_type_id');
        $page = $request->input('page', 1);

        $accountType = AccountType::query()->find($accountTypeId);

        if (!$accountType) {
            return response()->json(['error' => 'Account type not found'], 404);
        }

        // Always process material list for preview
        $this->processMaterialList($accountType, $page);

        // Build existingMaterials as content => id
        $existingMaterials = Material::all()->pluck('id', 'content')->toArray();

        // Return the view with just the materials list
        return view('account_type.materials', compact('accountType', 'existingMaterials'));
    }

    /**
     * Process material list for an account type
     *
     * This helper method processes the material list for an account type,
     * counts non-existent materials, and sets up pagination.
     *
     * @param \App\Models\AccountType $accountType
     * @param int|null $page Custom page number for AJAX requests
     * @return void
     */
    protected function processMaterialList($accountType, $page = null)
    {
        if (is_array($accountType->materialList) && !empty($accountType->materialList)) {
            // Convert material list to collection for pagination
            $collection = collect($accountType->materialList);

            // Build existingMaterials as content => id
            $existingMaterials = Material::all()->pluck('id', 'content')->toArray();

            // Count non-existent materials
            $nonExistentCount = 0;
            foreach ($accountType->materialList as $material) {
                if (!array_key_exists($material, $existingMaterials)) {
                    $nonExistentCount++;
                }
            }

            // Store the count
            $accountType->nonExistentCount = $nonExistentCount;

            // Create a LengthAwarePaginator manually
            $perPage = 50; // Show 50 items per page
            $currentPage = $page ?? request()->input('page_' . $accountType->id, 1);

            $currentItems = $collection->slice(($currentPage - 1) * $perPage, $perPage)->all();

            $accountType->paginatedMaterials = new \Illuminate\Pagination\LengthAwarePaginator(
                $currentItems,
                count($collection),
                $perPage,
                $currentPage,
                [
                    'path' => request()->url(),
                    'query' => request()->query(),
                    'pageName' => $page ? 'page' : 'page_' . $accountType->id
                ]
            );
        } else {
            $accountType->nonExistentCount = 0;
        }
    }

    /**
     * Manually update materials for an account type
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateMaterials(Request $request, $id)
    {
        $this->authorize('edit-account', AccountType::class);
        $accountType = AccountType::findOrFail($id);
        DB::beginTransaction();
        // For each course, update its lesson_list independently
        $materialsInput = $request->input('materials', []);
        foreach ($accountType->courses as $course) {
            $cm = AccountTypeCourseMaterial::firstOrNew([
                'account_type_id' => $accountType->id,
                'course_id' => $course->id,
            ]);
            $currentMaterials = isset($materialsInput[$course->id]) ? $materialsInput[$course->id] : [];
            // Use the course's material_prefix for new codes
            $prefix = $course->material_prefix;
            $maxMaterials = $accountType->max_materials;
            $addCount = $accountType->materials_add_count;
            // Find the highest suffix for this prefix
            $last = 0;
            foreach ($currentMaterials as $mat) {
                if (strpos($mat, $prefix) === 0) {
                    $suffix = intval(substr($mat, strlen($prefix)));
                    if ($suffix > $last) $last = $suffix;
                }
            }
            // Add new materials incrementally (e.g., mgn-02, mgn-03, ...)
            for ($i = 1; $i <= $addCount; $i++) {
                $last++;
                $currentMaterials[] = $prefix . str_pad($last, 2, '0', STR_PAD_LEFT);
            }
            // Keep only the last $maxMaterials
            $currentMaterials = array_slice($currentMaterials, -$maxMaterials);
            $cm->lesson_list = implode(',', $currentMaterials);
            $cm->save();
        }
        // After updating per-course lesson_list, update the global lesson_list to be the union of all per-course lists
        $allMaterials = AccountTypeCourseMaterial::where('account_type_id', $accountType->id)
            ->pluck('lesson_list')
            ->filter()
            ->flatMap(function ($list) {
                return explode(',', $list);
            })
            ->unique()
            ->filter()
            ->values()
            ->all();
        $accountType->lesson_list = implode(',', $allMaterials);
        $accountType->last_materials_update = now();
        $accountType->save();
        DB::commit();
        return response()->json(['message' => __('messages.update_started')]);
    }

    public function clearMaterials($id)
    {
        $this->authorize('edit-account', \App\Models\AccountType::class);
        $accountType = \App\Models\AccountType::findOrFail($id);
        DB::beginTransaction();
        // Clear all per-course materials (lesson_list)
        \App\Models\AccountTypeCourseMaterial::where('account_type_id', $accountType->id)
            ->update(['lesson_list' => '']);
        // Also clear the global lesson_list
        $accountType->lesson_list = '';
        $accountType->save();
        DB::commit();
        return response()->json([
            'success' => true,
            'message' => __('messages.materials_cleared')
        ]);
    }
}
