<?php

namespace App\Http\Controllers;

use App\Http\Helpers;
use App\Models\Account;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

// Commented out pending account feature
class AccountPendingController extends Controller
{
    /**
     * @throws AuthorizationException
     */
    /*
    public function index(Request $request)
    {
        $this->authorize('show-account', Account::class);
        $keyword = $request->input('search');

        $accountQuery = Account::query()
            ->whereIn('status', [Account::STATUS_PENDING])
            ->where('deleted_at', null);

        if($keyword != null){
            $accountQuery = $accountQuery->where(function ($query) use ($keyword) {
                $query->where('name', 'like', "%$keyword%")
                    ->orWhere('email', 'like', "%$keyword%")
                    ->orWhere('name_org', 'like', "%$keyword%")
                    ->orWhere('phone_number', 'like', "%$keyword%");
            });
        }

        $accounts = $accountQuery
            ->sortable()
            ->paginate(100);

        $provinces = [];
        $districts = [];
        try{
            $provinces = Helpers::getProvinces();
            $districts = Helpers::getDistricts();
        } catch (\Exception $e) {
        }

        return view('account_pending.index', compact('accounts',  'keyword', 'provinces', 'districts'));
    }

    public function accept(Request $request, $id)
    {
        $this->authorize('edit-account', Account::class);
        $accountPending = Account::query()
            ->whereIn('status', [Account::STATUS_PENDING])
            ->where('deleted_at', null)
            ->where('id', $id)
            ->first()
        ;

        if($accountPending == null){
            $this->flashMessage('warning', 'Account not found!', 'danger');
            return redirect()->route('account_pending');
        }

        $accountPending->status = Account::STATUS_ACTIVE;
        $accountPending->save();

        return redirect()->route('account.show', ['id' => $accountPending->id]);
    }

    public function destroy($id): RedirectResponse
    {
        $this->authorize('destroy-account', Account::class);
        $account = Account::query()
            ->where('status', Account::STATUS_PENDING)
            ->find($id);

        if(!$account){
            $this->flashMessage('warning', 'Account not found!', 'danger');
            return redirect()->route('account_pending');
        }

        $account->delete();
        $this->flashMessage('check', 'Account successfully deleted!', 'success');

        return redirect()->route('account_pending');
    }
    */
}
