<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\MaterialTitleMapping;
use App\Models\Material;
use App\Models\AccountType;
use Illuminate\Support\Facades\DB;

class MaterialTitleMappingController extends Controller
{
    /**
     * Display a listing of material title mappings.
     */
    public function index(Request $request)
    {
        $this->authorize('show-course', Material::class);

        $accountTypeId = $request->input('account_type_id');
        $materialId = $request->input('material_id');

        $query = MaterialTitleMapping::query()
            ->with(['material.lesson.course', 'accountType']);

        if ($accountTypeId) {
            $query->where('account_type_id', $accountTypeId);
        }

        if ($materialId) {
            $query->where('material_id', $materialId);
        }

        $mappings = $query->paginate(50);
        $accountTypes = AccountType::all();
        $materials = Material::with('lesson.course')->get();

        return view('material_title_mapping.index', compact('mappings', 'accountTypes', 'materials', 'accountTypeId', 'materialId'));
    }

    /**
     * Show the form for creating a new material title mapping.
     */
    public function create()
    {
        $this->authorize('create-course', Material::class);

        $materials = Material::with('lesson.course')->get();
        $accountTypes = AccountType::all();

        return view('material_title_mapping.create', compact('materials', 'accountTypes'));
    }

    /**
     * Store a newly created material title mapping in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create-course', Material::class);

        $request->validate([
            'material_id' => 'required|exists:materials,id',
            'account_type_id' => 'required|exists:account_types,id',
            'prefix_number' => 'required|string|max:10',
            'prefix_type' => 'required|string|max:20',
        ]);

        try {
            MaterialTitleMapping::create([
                'material_id' => $request->material_id,
                'account_type_id' => $request->account_type_id,
                'prefix_number' => $request->prefix_number,
                'prefix_type' => $request->prefix_type,
            ]);

            return redirect()->route('material_title_mapping.index')
                ->with('success', 'Material title mapping created successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error creating mapping: This material already has a mapping for this account type.');
        }
    }

    /**
     * Show the form for editing the specified material title mapping.
     */
    public function edit($id)
    {
        $this->authorize('edit-course', Material::class);

        $mapping = MaterialTitleMapping::with(['material.lesson.course', 'accountType'])->findOrFail($id);
        $materials = Material::with('lesson.course')->get();
        $accountTypes = AccountType::all();

        return view('material_title_mapping.edit', compact('mapping', 'materials', 'accountTypes'));
    }

    /**
     * Update the specified material title mapping in storage.
     */
    public function update(Request $request, $id)
    {
        $this->authorize('edit-course', Material::class);

        $mapping = MaterialTitleMapping::findOrFail($id);

        $request->validate([
            'material_id' => 'required|exists:materials,id',
            'account_type_id' => 'required|exists:account_types,id',
            'prefix_number' => 'required|string|max:10',
            'prefix_type' => 'required|string|max:20',
        ]);

        try {
            $mapping->update([
                'material_id' => $request->material_id,
                'account_type_id' => $request->account_type_id,
                'prefix_number' => $request->prefix_number,
                'prefix_type' => $request->prefix_type,
            ]);

            return redirect()->route('material_title_mapping.index')
                ->with('success', 'Material title mapping updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error updating mapping: This material already has a mapping for this account type.');
        }
    }

    /**
     * Remove the specified material title mapping from storage.
     */
    public function destroy($id)
    {
        $this->authorize('edit-course', Material::class);

        $mapping = MaterialTitleMapping::findOrFail($id);
        $mapping->delete();

        return redirect()->route('material_title_mapping.index')
            ->with('success', 'Material title mapping deleted successfully!');
    }

    /**
     * Bulk create mappings for a material across multiple account types.
     */
    public function bulkCreate(Request $request)
    {
        $this->authorize('create-course', Material::class);

        $request->validate([
            'material_id' => 'required|exists:materials,id',
            'mappings' => 'required|array',
            'mappings.*.account_type_id' => 'required|exists:account_types,id',
            'mappings.*.prefix_number' => 'required|string|max:10',
            'mappings.*.prefix_type' => 'required|string|max:20',
        ]);

        DB::beginTransaction();

        try {
            foreach ($request->mappings as $mappingData) {
                MaterialTitleMapping::updateOrCreate(
                    [
                        'material_id' => $request->material_id,
                        'account_type_id' => $mappingData['account_type_id'],
                    ],
                    [
                        'prefix_number' => $mappingData['prefix_number'],
                        'prefix_type' => $mappingData['prefix_type'],
                    ]
                );
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Material title mappings created/updated successfully!'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error creating mappings: ' . $e->getMessage()
            ], 500);
        }
    }
}
