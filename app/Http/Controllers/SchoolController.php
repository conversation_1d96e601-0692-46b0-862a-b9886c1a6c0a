<?php

namespace App\Http\Controllers;

use App\Http\Helpers;
use App\Http\Requests\School\StoreRequest;
use App\Http\Requests\School\UpdateRequest;
use App\Models\Account;
use App\Models\AccountType;
use App\Models\School;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SchoolController extends Controller
{
    /**
     * Display a listing of the schools.
     *
     * @param Request $request
     * @return \Illuminate\Contracts\View\View
     * @throws AuthorizationException
     */
    public function index(Request $request)
    {
        $this->authorize('show-account', School::class);

        $keyword = $request->input('search');
        $searchProvince = $request->input('province');
        $searchDistrict = $request->input('district');

        $provinces = [];
        $districts = [];
        $selectedDistricts = [];

        try {
            $provinces = Helpers::getProvinces();
            $districts = Helpers::getDistricts();

            if ($searchProvince != null && $searchProvince != -1) {
                $selectedDistricts = Helpers::getDistrictsByProvinceId($searchProvince);
            }
        } catch (\Exception $e) {
        }

        $query = School::query()->sortable();

        // Apply filters
        if ($searchProvince != null && $searchProvince != -1) {
            $query = $query->where('province', $searchProvince);
        }

        if ($searchDistrict != null && $searchDistrict != -1) {
            $query = $query->where('district', $searchDistrict);
        }

        if ($keyword != null) {
            $query = $query->where('name', 'like', "%$keyword%");
        }

        $schools = $query->paginate(100);

        return view('school.index', compact('schools', 'provinces', 'districts', 'searchDistrict',
            'searchProvince', 'selectedDistricts', 'keyword'));
    }

    /**
     * Show the form for creating a new school.
     *
     * @return \Illuminate\Contracts\View\View
     * @throws AuthorizationException
     */
    public function create()
    {
        $this->authorize('create-account', School::class);

        $provinces = [];
        $firstDistricts = [];

        try {
            $provinces = Helpers::getProvinces();

            if (count($provinces) > 0) {
                $firstDistricts = Helpers::getDistrictsByProvinceId(array_keys($provinces)[0]);
            }
        } catch (\Exception $e) {
        }

        $mamnonAccountTypes = AccountType::all();
        $tieuhocAccountTypes = AccountType::all();

        return view('school.create', compact('provinces', 'firstDistricts', 'mamnonAccountTypes', 'tieuhocAccountTypes'));
    }

    /**
     * Store a newly created school in storage.
     *
     * @param StoreRequest $request
     * @return RedirectResponse
     * @throws AuthorizationException
     */
    public function store(StoreRequest $request): RedirectResponse
    {
        $this->authorize('create-account', School::class);

        // init software type
        $softwareTypes = $request->input('software_types');
        $isMamnon = 0;
        $isTieuhoc = 0;
        $mamnonAccountTypeId = null;
        $tieuhocAccountTypeId = null;

        if (in_array(Account::SOFTWARE_MAMNON, $softwareTypes)) {
            $isMamnon = 1;
            $mamnonAccountTypeId = $request->input('mamnon_account_type');
        }

        if (in_array(Account::SOFTWARE_TIEUHOC, $softwareTypes)) {
            $isTieuhoc = 1;
            $tieuhocAccountTypeId = $request->input('tieuhoc_account_type');
        }

        $school = School::create([
            'name' => $request->input('name'),
            'province' => $request->input('province'),
            'district' => $request->input('district'),
            'time_expired' => $request->input('time_expired'),
            'is_mamnon' => $isMamnon,
            'is_tieuhoc' => $isTieuhoc,
            'mamnon_account_type_id' => $mamnonAccountTypeId,
            'tieuhoc_account_type_id' => $tieuhocAccountTypeId,
        ]);

        $this->flashMessage('check', __('messages.alert.add.successfully'), 'success');
        return redirect()->route('school.show', ['id' => $school->id]);
    }

    /**
     * Display the specified school.
     *
     * @param int $id
     * @return \Illuminate\Contracts\View\View|RedirectResponse
     * @throws AuthorizationException
     */
    public function show($id)
    {
        $this->authorize('show-account', School::class);

        $school = School::find($id);

        if (!$school) {
            $this->flashMessage('warning', __('messages.alert.not_found'), 'danger');
            return redirect()->route('school');
        }

        $provinces = [];
        $districts = [];
        $schoolDistricts = [];

        try {
            $provinces = Helpers::getProvinces();
            $districts = Helpers::getDistricts();

            if ($school->province) {
                $schoolDistricts = Helpers::getDistrictsByProvinceId($school->province);
            }
        } catch (\Exception $e) {
        }

        // Get accounts associated with this school
        $accounts = Account::where('school_id', $school->id)->paginate(15);

        // Get account types for editing
        $mamnonAccountTypes = AccountType::all();
        $tieuhocAccountTypes = AccountType::all();

        // Get schools grouped by province and district for the dashboard
        $schoolsByLocation = School::select('province', 'district', DB::raw('count(*) as total'))
            ->groupBy('province', 'district')
            ->orderBy('province')
            ->orderBy('district')
            ->get()
            ->groupBy('province');

        // Get province and district names
        $provinceNames = $provinces;
        $districtNames = $districts;

        return view('school.show', compact(
            'school',
            'accounts',
            'provinces',
            'districts',
            'schoolDistricts',
            'mamnonAccountTypes',
            'tieuhocAccountTypes',
            'schoolsByLocation',
            'provinceNames',
            'districtNames'
        ));
    }

    /**
     * Show the form for editing the specified school.
     *
     * @param int $id
     * @return \Illuminate\Contracts\View\View|RedirectResponse
     * @throws AuthorizationException
     */
    public function edit($id)
    {
        $this->authorize('edit-account', School::class);

        // Redirect to show view since we've combined show and edit functionality
        return redirect()->route('school.show', ['id' => $id]);
    }

    /**
     * Update the specified school in storage.
     *
     * @param UpdateRequest $request
     * @param int $id
     * @return RedirectResponse
     * @throws AuthorizationException
     */
    public function update(UpdateRequest $request, $id): RedirectResponse
    {
        $this->authorize('edit-account', School::class);

        $school = School::find($id);

        if (!$school) {
            $this->flashMessage('warning', __('messages.alert.not_found'), 'danger');
            return redirect()->route('school');
        }

        // init software type
        $softwareTypes = $request->input('software_types');
        $isMamnon = 0;
        $isTieuhoc = 0;
        $mamnonAccountTypeId = null;
        $tieuhocAccountTypeId = null;

        if (in_array(Account::SOFTWARE_MAMNON, $softwareTypes)) {
            $isMamnon = 1;
            $mamnonAccountTypeId = $request->input('mamnon_account_type');
        }

        if (in_array(Account::SOFTWARE_TIEUHOC, $softwareTypes)) {
            $isTieuhoc = 1;
            $tieuhocAccountTypeId = $request->input('tieuhoc_account_type');
        }

        DB::beginTransaction();

        try {
            // Parse time_expired with time component
            $timeExpired = null;
            if ($request->has('time_expired') && !empty($request->input('time_expired'))) {
                $timeExpired = \DateTime::createFromFormat("Y-m-d\TH:i", $request->input('time_expired'));
            }

            // Update school
            $school->update([
                'name' => $request->input('name'),
                'province' => $request->input('province'),
                'district' => $request->input('district'),
                'time_expired' => $timeExpired ? $timeExpired->format('Y-m-d H:i:s') : null,
                'is_mamnon' => $isMamnon,
                'is_tieuhoc' => $isTieuhoc,
                'mamnon_account_type_id' => $mamnonAccountTypeId,
                'tieuhoc_account_type_id' => $tieuhocAccountTypeId,
                'phone_number_as_password' => $request->has('phone_number_as_password') ? $request->input('phone_number_as_password') : 0,
            ]);

            // Log the update for debugging
            \Log::info('School updated', [
                'school_id' => $school->id,
                'time_expired' => $timeExpired ? $timeExpired->format('Y-m-d H:i:s') : null,
                'is_mamnon' => $isMamnon,
                'is_tieuhoc' => $isTieuhoc,
                'mamnon_account_type_id' => $mamnonAccountTypeId,
                'tieuhoc_account_type_id' => $tieuhocAccountTypeId,
                'phone_number_as_password' => $request->input('phone_number_as_password', 1),
            ]);

            // Update all accounts associated with this school
            $phoneAsPassword = $request->has('phone_number_as_password') ? $request->input('phone_number_as_password') : 0;
            $updatedAccounts = Account::where('school_id', $school->id)->get();
            foreach ($updatedAccounts as $account) {
                $account->phone_number_as_password = $phoneAsPassword;
                if ($phoneAsPassword) {
                    $account->password = \Hash::make($account->phone_number);
                    $account->password_manual = null;
                }
                $account->save();
            }

            // Log the number of accounts updated
            \Log::info('Associated accounts updated', [
                'school_id' => $school->id,
                'accounts_updated' => $updatedAccounts->count(),
                'time_expired' => $timeExpired ? $timeExpired->format('Y-m-d H:i:s') : null
            ]);

            // Check if a bulk action was requested
            if ($request->has('bulk_action')) {
                $bulkAction = $request->input('bulk_action');
                $newStatus = ($bulkAction === 'unlock') ? Account::STATUS_ACTIVE : Account::STATUS_LOCK;

                // Update all accounts associated with this school
                \Log::info('Applying bulk action to all accounts', [
                    'school_id' => $school->id,
                    'action' => $bulkAction,
                    'new_status' => $newStatus
                ]);

                Account::where('school_id', $school->id)->update(['status' => $newStatus]);
            }
            // Otherwise, update individual account statuses if provided
            else if ($request->has('account_statuses') && is_array($request->input('account_statuses'))) {
                foreach ($request->input('account_statuses') as $accountId => $status) {
                    $account = Account::find($accountId);
                    if ($account && $account->school_id == $school->id) {
                        $account->status = $status;
                        $account->save();
                    }
                }
            }

            DB::commit();

            $this->flashMessage('check', __('messages.alert.edit.successfully'), 'success');
            return redirect()->route('school.show', ['id' => $id]);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->flashMessage('warning', __('messages.alert.edit.error'), 'danger');
            return redirect()->route('school.show', ['id' => $id]);
        }
    }

    /**
     * Remove the specified school from storage.
     *
     * @param int $id
     * @return RedirectResponse
     * @throws AuthorizationException
     */
    public function destroy($id): RedirectResponse
    {
        $this->authorize('destroy-account', School::class);

        $school = School::find($id);

        if (!$school) {
            $this->flashMessage('warning', __('messages.alert.not_found'), 'danger');
            return redirect()->route('account', ['view_mode' => 'school']);
        }

        // Set school_id to null for all associated accounts
        Account::where('school_id', $school->id)->update(['school_id' => null]);

        $school->delete();

        $this->flashMessage('check', __('messages.alert.delete.successfully'), 'success');
        return redirect()->route('account', ['view_mode' => 'school']);
    }

    /**
     * Bulk delete schools
     *
     * @param \Illuminate\Http\Request $request
     * @return RedirectResponse
     */
    public function bulkDestroy(Request $request): RedirectResponse
    {
        $this->authorize('destroy-account', School::class);
        $ids = explode(',', $request->input('ids'));

        if (empty($ids)) {
            $this->flashMessage('warning', 'No schools selected!', 'danger');
            return redirect()->route('account', ['view_mode' => 'school']);
        }

        DB::beginTransaction();
        $deletedCount = 0;
        
        foreach ($ids as $id) {
            $school = School::find($id);
            if ($school) {
                // Set school_id to null for all associated accounts
                Account::where('school_id', $school->id)->update(['school_id' => null]);
                
                $school->delete();
                $deletedCount++;
            }
        }

        DB::commit();

        if ($deletedCount > 0) {
            $this->flashMessage('check', $deletedCount . ' schools successfully deleted!', 'success');
        } else {
            $this->flashMessage('warning', 'No schools were deleted!', 'danger');
        }

        return redirect()->route('account', ['view_mode' => 'school']);
    }

    /**
     * Get the status of all accounts associated with a school.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     * @throws AuthorizationException
     */
    public function getAccountsStatus($id)
    {
        $this->authorize('show-account', School::class);

        $school = School::find($id);

        if (!$school) {
            return response()->json(['success' => false, 'message' => __('messages.alert.not_found')]);
        }

        // Get all accounts associated with this school
        $accounts = Account::where('school_id', $school->id)->get();

        if ($accounts->isEmpty()) {
            return response()->json(['success' => true, 'status' => 'no_accounts']);
        }

        // Check if all accounts are locked
        $allLocked = $accounts->every(function ($account) {
            return $account->status == Account::STATUS_LOCK;
        });

        // Check if all accounts are unlocked
        $allUnlocked = $accounts->every(function ($account) {
            return $account->status == Account::STATUS_ACTIVE;
        });

        // Determine status
        $status = 'mixed';
        if ($allLocked) {
            $status = 'all_locked';
        } elseif ($allUnlocked) {
            $status = 'all_unlocked';
        }

        return response()->json([
            'success' => true,
            'status' => $status,
            'total_accounts' => $accounts->count(),
            'locked_accounts' => $accounts->where('status', Account::STATUS_LOCK)->count(),
            'unlocked_accounts' => $accounts->where('status', Account::STATUS_ACTIVE)->count()
        ]);
    }

    /**
     * Toggle the status of all accounts associated with a school.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     * @throws AuthorizationException
     */
    public function toggleAccountsStatus(Request $request, $id)
    {
        $this->authorize('edit-account', School::class);

        $school = School::find($id);

        if (!$school) {
            return response()->json(['success' => false, 'message' => __('messages.alert.not_found')]);
        }

        $status = $request->input('status', 'lock');
        $newStatus = ($status === 'unlock') ? Account::STATUS_ACTIVE : Account::STATUS_LOCK;

        // Update all accounts associated with this school
        Account::where('school_id', $school->id)->update(['status' => $newStatus]);

        return response()->json([
            'success' => true,
            'status' => ($newStatus == Account::STATUS_ACTIVE) ? 'all_unlocked' : 'all_locked',
            'message' => __('messages.alert.update.successfully')
        ]);
    }

    /**
     * Sync all accounts' info with the school info for a given school.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncAccountsWithSchool(Request $request, $id)
    {
        $this->authorize('edit-account', School::class);

        $school = School::find($id);
        if (!$school) {
            return response()->json(['success' => false, 'message' => __('messages.alert.not_found')]);
        }

        // Define which fields to sync
        $fieldsToSync = [
            'province',
            'district',
            'mamnon_account_type_id',
            'tieuhoc_account_type_id',
            'time_expired',
            'phone_number_as_password', // Add this field
        ];

        $accounts = $school->accounts;
        foreach ($accounts as $account) {
            foreach ($fieldsToSync as $field) {
                $account->$field = $school->$field;
            }
            $account->sync_with_school = true;
            // If phone_number_as_password is true, update password to phone number
            if ($school->phone_number_as_password) {
                $account->password = \Hash::make($account->phone_number);
                $account->password_manual = null;
            }
            $account->save();
        }

        return response()->json(['success' => true, 'message' => __('messages.sync_success')]);
    }
}
