<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        \App\Console\Commands\BackupAccountDataCommand::class,
        \App\Console\Commands\ManageAccountTypeMaterialsCommand::class,
        \App\Console\Commands\LockExpiredAccountsCommand::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();
        $schedule->command('account:backup')->daily();

        // Schedule the material management command to run daily at 1 AM
        $schedule->command('account-type:manage-materials --max-materials=50 --add-count=5')
            ->dailyAt('01:00')
            ->appendOutputTo(storage_path('logs/material-management.log'));

        // Schedule the lock expired accounts command to run daily at 2 AM
        $schedule->command('account:lock-expired')
            ->dailyAt('02:00')
            ->appendOutputTo(storage_path('logs/lock-expired-accounts.log'));
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
