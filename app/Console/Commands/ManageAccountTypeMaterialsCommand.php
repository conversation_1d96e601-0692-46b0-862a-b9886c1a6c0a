<?php

namespace App\Console\Commands;

use App\Jobs\ManageAccountTypeMaterialsJob;
use App\Models\AccountType;
use Illuminate\Console\Command;

class ManageAccountTypeMaterialsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'account-type:manage-materials
                            {--account-type-id= : The ID of the account type to process (optional, processes all if not specified)}
                            {--max-materials=50 : Maximum number of materials to keep per account type}
                            {--add-count=5 : Number of new materials to add per account type}
                            {--dry-run : Run without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage materials for account types - add new materials and remove old ones';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $accountTypeId = $this->option('account-type-id');
        $maxMaterials = (int)$this->option('max-materials');
        $addCount = (int)$this->option('add-count');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('Running in dry-run mode - no changes will be made');
        }

        // Get account types to process
        if ($accountTypeId) {
            $accountTypes = AccountType::where('id', $accountTypeId)->get();
            if ($accountTypes->isEmpty()) {
                $this->error("Account type with ID {$accountTypeId} not found");
                return 1;
            }
        } else {
            // Only get account types with auto_manage_materials enabled
            $query = AccountType::query();

            // If not in dry-run mode, only get account types that are due for an update
            if (!$dryRun) {
                $query->where('auto_manage_materials', true);

                // Filter by update frequency
                $query->where(function($q) {
                    // Include account types that have never been updated
                    $q->whereNull('last_materials_update');

                    // Daily updates
                    $q->orWhere(function($q) {
                        $q->where('materials_update_frequency', 'daily')
                          ->where('last_materials_update', '<', now()->subDay());
                    });

                    // Weekly updates
                    $q->orWhere(function($q) {
                        $q->where('materials_update_frequency', 'weekly')
                          ->where('last_materials_update', '<', now()->subWeek());
                    });

                    // Monthly updates
                    $q->orWhere(function($q) {
                        $q->where('materials_update_frequency', 'monthly')
                          ->where('last_materials_update', '<', now()->subMonth());
                    });
                });
            }

            $accountTypes = $query->get();

            if ($accountTypes->isEmpty()) {
                $this->info('No account types found that need updating');
                return 0;
            }
        }

        $this->info("Processing " . $accountTypes->count() . " account type(s)");

        $bar = $this->output->createProgressBar($accountTypes->count());
        $bar->start();

        foreach ($accountTypes as $accountType) {
            $this->processAccountType($accountType, $maxMaterials, $addCount, $dryRun);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('Completed processing account types');

        return 0;
    }

    /**
     * Process a single account type
     *
     * @param AccountType $accountType
     * @param int $maxMaterials
     * @param int $addCount
     * @param bool $dryRun
     * @return void
     */
    protected function processAccountType(AccountType $accountType, int $maxMaterials, int $addCount, bool $dryRun)
    {
        $this->newLine();
        $this->info("Processing account type: {$accountType->name} (ID: {$accountType->id})");

        // Dispatch the job to handle the actual processing
        if (!$dryRun) {
            ManageAccountTypeMaterialsJob::dispatch($accountType, $maxMaterials, $addCount);
            $this->info("Job dispatched for account type {$accountType->id}");
        } else {
            // In dry-run mode, we'll just show what would happen
            $this->simulateProcessing($accountType, $maxMaterials, $addCount);
        }
    }

    /**
     * Simulate processing for dry-run mode
     *
     * @param AccountType $accountType
     * @param int $maxMaterials
     * @param int $addCount
     * @return void
     */
    protected function simulateProcessing(AccountType $accountType, int $maxMaterials, int $addCount)
    {
        // Get current materials
        $currentMaterials = [];
        if ($accountType->lesson_list) {
            $currentMaterials = explode(',', $accountType->lesson_list);
        }

        $currentCount = count($currentMaterials);

        $this->info("  Current material count: {$currentCount}");

        // Calculate how many materials would be removed
        $removeCount = max(0, $currentCount + $addCount - $maxMaterials);
        if ($removeCount > 0) {
            $this->info("  Would remove {$removeCount} oldest materials");
            $this->info("  Materials that would be removed: " . implode(', ', array_slice($currentMaterials, 0, $removeCount)));
        } else {
            $this->info("  No materials would be removed");
        }

        // Show how many materials would be added
        $this->info("  Would add {$addCount} new materials");

        // Calculate the new total
        $newTotal = $currentCount + $addCount - $removeCount;
        $this->info("  New material count would be: {$newTotal}");
    }
}
