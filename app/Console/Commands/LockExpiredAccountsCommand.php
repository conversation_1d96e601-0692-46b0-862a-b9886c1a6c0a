<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Account;
use Carbon\Carbon;

class LockExpiredAccountsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'account:lock-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Lock all accounts whose expiration date has passed.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $now = Carbon::now();
        $accounts = Account::where('status', Account::STATUS_ACTIVE)
            ->whereNotNull('time_expired')
            ->where('time_expired', '<', $now)
            ->get();

        $count = 0;
        foreach ($accounts as $account) {
            $account->status = Account::STATUS_LOCK;
            $account->save();
            $count++;
        }

        $this->info("Locked {$count} expired account(s).");
        return 0;
    }
} 