<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\School;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportSchoolsFromAccounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:schools-from-accounts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import schools from accounts name_org field';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting import of schools from accounts...');

        // Get unique name_org values from accounts
        $uniqueSchools = Account::whereNotNull('name_org')
            ->where('name_org', '!=', '')
            ->select('name_org', 'province', 'district', 'time_expired', 'is_mamnon', 'is_tieuhoc', 'mamnon_account_type_id', 'tieuhoc_account_type_id')
            ->distinct('name_org')
            ->get();

        $this->info("Found {$uniqueSchools->count()} unique schools to import.");

        $bar = $this->output->createProgressBar($uniqueSchools->count());
        $bar->start();

        $schoolsCreated = 0;
        $accountsUpdated = 0;

        DB::beginTransaction();

        try {
            foreach ($uniqueSchools as $schoolData) {
                // Trim the school name to handle any whitespace issues
                $schoolName = trim($schoolData->name_org);

                // Check if school already exists
                $school = School::where('name', $schoolName)->first();

                if (!$school) {
                    // Create new school with default values for nullable fields
                    $school = School::create([
                        'name' => $schoolName,
                        'province' => $schoolData->province,
                        'district' => $schoolData->district,
                        'time_expired' => $schoolData->time_expired,
                        'is_mamnon' => $schoolData->is_mamnon ?? 0,
                        'is_tieuhoc' => $schoolData->is_tieuhoc ?? 0,
                        'mamnon_account_type_id' => $schoolData->mamnon_account_type_id,
                        'tieuhoc_account_type_id' => $schoolData->tieuhoc_account_type_id,
                    ]);

                    $schoolsCreated++;
                }

                // Update all accounts with this name_org to link to the school
                $updatedCount = Account::where('name_org', $schoolName)
                    ->update(['school_id' => $school->id]);

                $accountsUpdated += $updatedCount;

                $bar->advance();
            }

            DB::commit();
            $bar->finish();

            $this->newLine();
            $this->info("Import completed successfully!");
            $this->info("Created {$schoolsCreated} new schools.");
            $this->info("Updated {$accountsUpdated} accounts with school associations.");

            return 0;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->newLine();
            $this->error("An error occurred during import: " . $e->getMessage());
            return 1;
        }
    }
}
