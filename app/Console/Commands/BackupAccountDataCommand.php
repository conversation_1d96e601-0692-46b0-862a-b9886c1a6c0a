<?php

namespace App\Console\Commands;

use App\Jobs\BackupAccountDataJob;
use Illuminate\Console\Command;

class BackupAccountDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'account:backup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        BackupAccountDataJob::dispatch();
    }
}
